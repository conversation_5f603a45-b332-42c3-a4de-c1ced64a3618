import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Navigation } from "@/components/navigation";
import { PageHeader } from "@/components/page-header";
import { locales } from '@/i18n';

export function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}

export default function PrivacyPolicy() {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <Navigation />
      <PageHeader
        title="Privacy Policy"
        description="Learn how we protect your privacy and data security"
      />

      {/* Content */}
      <main className="container mx-auto px-4 py-8 max-w-4xl">
        <Card className="border-border">
          <CardHeader>
            <CardTitle className="text-3xl text-foreground">RayBoxUI Privacy Policy</CardTitle>
            <p className="text-muted-foreground">Last updated: August 22, 2024</p>
          </CardHeader>
          <CardContent className="space-y-6">
            <section>
              <h2 className="text-xl font-semibold mb-3 text-foreground">1. Information Collection</h2>
              <p className="text-muted-foreground leading-relaxed">
                RayBoxUI is a locally running node management tool. We do not collect, store, or transmit your personal information to our servers.
                All configuration data is stored on your local device.
              </p>
            </section>

            <section>
              <h2 className="text-xl font-semibold mb-3 text-foreground">2. Data Usage</h2>
              <p className="text-muted-foreground leading-relaxed">
                All configuration information you enter in the application (including server addresses, usernames, passwords, etc.) is only used for:
              </p>
              <ul className="list-disc list-inside mt-2 space-y-1 text-muted-foreground">
                <li>Connecting to your specified node management panels</li>
                <li>Generating configuration files and QR codes</li>
                <li>Local storage of configurations on your device</li>
              </ul>
            </section>

            <section>
              <h2 className="text-xl font-semibold mb-3 text-foreground">3. Data Security</h2>
              <p className="text-muted-foreground leading-relaxed">
                We take the following measures to protect your data security:
              </p>
              <ul className="list-disc list-inside mt-2 space-y-1 text-muted-foreground">
                <li>All sensitive data is stored only on your local device</li>
                <li>Support HTTPS connections to ensure transmission security</li>
                <li>Support certificate fingerprint verification to prevent man-in-the-middle attacks</li>
                <li>We do not send your configuration information to third-party servers</li>
              </ul>
            </section>

            <section>
              <h2 className="text-xl font-semibold mb-3 text-foreground">4. Third-Party Services</h2>
              <p className="text-muted-foreground leading-relaxed">
                RayBoxUI may connect to third-party node management panels you specify (such as x-ui, 3x-ui, s-ui).
                These connections are completely under your control, and we do not monitor or record the content of these connections.
              </p>
            </section>

            <section>
              <h2 className="text-xl font-semibold mb-3 text-foreground">5. Data Deletion</h2>
              <p className="text-muted-foreground leading-relaxed">
                You can delete all configuration data stored in the application at any time. Uninstalling the application will automatically delete all locally stored data.
              </p>
            </section>

            <section>
              <h2 className="text-xl font-semibold mb-3 text-foreground">6. Policy Updates</h2>
              <p className="text-muted-foreground leading-relaxed">
                We may update this privacy policy from time to time. Any changes will be posted on this page and the &ldquo;Last updated&rdquo; date will be updated.
              </p>
            </section>

            <section>
              <h2 className="text-xl font-semibold mb-3 text-foreground">7. Contact Us</h2>
              <p className="text-muted-foreground leading-relaxed">
                If you have any questions about this privacy policy, please contact us through the feedback feature in the application.
              </p>
            </section>

            <div className="mt-8 p-4 bg-muted rounded-lg border border-border">
              <p className="text-sm text-foreground">
                <strong>Important Notice:</strong>
                RayBoxUI is a privacy-focused application. We promise not to collect, store, or share your personal data.
                All configuration information is securely stored on your device.
              </p>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  );
}
