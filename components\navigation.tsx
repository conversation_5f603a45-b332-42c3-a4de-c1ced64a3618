"use client";

import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { useTranslations, useLocale } from '@/components/i18n-provider';
import { Button } from "@/components/ui/button";
import { Menu, X, Globe } from "lucide-react";

export function Navigation() {
  const [isOpen, setIsOpen] = useState(false);
  const t = useTranslations();
  const locale = useLocale();

  return (
    <header className="bg-background border-b border-border">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-3">
            <Image
              src="/<EMAIL>"
              alt="RayBoxUI Logo"
              width={32}
              height={32}
              className="w-8 h-8 rounded-lg"
            />
            <h1 className="text-xl font-semibold text-foreground">{t('common.appName')}</h1>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-4">
            <Link href={`/${locale}/config`}>
              <Button variant="outline">{t('common.configNode')}</Button>
            </Link>
            <Link href={`/${locale}/privacy`}>
              <Button variant="ghost">{t('common.privacyPolicy')}</Button>
            </Link>
            <Link href={locale === 'zh' ? '/en' : '/zh'}>
              <Button variant="ghost" size="sm">
                <Globe className="w-4 h-4 mr-2" />
                {locale === 'zh' ? 'EN' : '中文'}
              </Button>
            </Link>
          </nav>

          {/* Mobile menu button */}
          <Button
            variant="ghost"
            size="sm"
            className="md:hidden"
            onClick={() => setIsOpen(!isOpen)}
          >
            {isOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
          </Button>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="md:hidden py-4 border-t border-border">
            <nav className="flex flex-col space-y-2">
              <Link href={`/${locale}/config`} onClick={() => setIsOpen(false)}>
                <Button variant="outline" className="w-full justify-start">
                  {t('common.configNode')}
                </Button>
              </Link>
              <Link href={`/${locale}/privacy`} onClick={() => setIsOpen(false)}>
                <Button variant="ghost" className="w-full justify-start">
                  {t('common.privacyPolicy')}
                </Button>
              </Link>
              <Link href={locale === 'zh' ? '/en' : '/zh'} onClick={() => setIsOpen(false)}>
                <Button variant="ghost" className="w-full justify-start">
                  <Globe className="w-4 h-4 mr-2" />
                  {locale === 'zh' ? 'EN' : '中文'}
                </Button>
              </Link>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
}
